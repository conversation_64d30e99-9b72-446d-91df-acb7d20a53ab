use solana_client::rpc_client::SerializableTransaction;
use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_client::rpc_config::RpcTransactionConfig;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::transaction::VersionedTransaction;

use solana_sdk::instruction::Instruction;
use solana_sdk::hash::Hash;

use solana_sdk::signer::keypair::Keypair;
use solana_sdk::system_instruction::transfer;
use solana_sdk::signature::Signer;
use solana_sdk::instruction::AccountMeta;
use solana_sdk::signature;
use solana_sdk::commitment_config::CommitmentConfig;

use solana_entry::entry::Entry;
use solana_transaction_status::UiTransactionEncoding;
use spl_token::instruction::close_account;
use spl_token::instruction::sync_native;
use std::collections::HashMap;

use std::str::FromStr;
use std::sync::atomic::AtomicPtr;
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicU64;
use std::sync::atomic::AtomicBool;
use spl_associated_token_account::get_associated_token_address;
use spl_associated_token_account::instruction::create_associated_token_account;
use solana_client::rpc_client::RpcClient;
use std::env;
use std::fs;
use std::sync::Arc;
use serde::{Deserialize};
use jito_protos::shredstream::{
    shredstream_proxy_client::ShredstreamProxyClient, SubscribeEntriesRequest,
};
use tokio::runtime::Runtime;
use std::time::{SystemTime, UNIX_EPOCH};
use chrono;
use reqwest::Client;
use std::sync::Mutex;

use serde_json::json;
use std::collections::HashSet;
use solana_trader_proto::api::TransactionMessage;

pub mod utils;
pub mod pump_fun_utils;
pub use utils::*;
pub use pump_fun_utils::*;

use std::fs::OpenOptions;
use std::io::Write;

mod websocket_service;

use websocket_service::WebSocketService;
use dotenv::dotenv;
use std::{thread, time::{Duration, Instant}};
use std::sync::mpsc;
use tokio::sync::broadcast;

#[derive(Debug, Clone)]
pub struct MonitoredToken {
    pub mint_key: String,
    pub dev_user_key: String, // dev user key as string
    pub curve: String,
    pub detected_at_slot: u64, // slot when detected
    pub token_create_time: u64, // timestamp when token was created
    pub token_create_signature: String, // signature when token was created
    pub dev_initial_buy_amount: Option<u64>, // amount of SOL used by dev for initial buy (None if not available)
    pub dev_sell_amount: u64, // amount of SOL when dev sold (0 if not sold yet)
    pub dev_sold_time: Option<String>, // timestamp when dev sold (None if not sold yet)
    pub twitterUrl: String,
    pub totalBuys: u64, // total number of buys
    pub lastBuyTime: u64, // timestamp of the last buy
    pub hasLargeBuy: bool, // true if a large buy has occurred, default false
    pub errorCount: u64, // number of errors encountered, default 0
    pub tokenSupply: u64, // total supply, initial value 0
}

#[derive(Debug, Clone)]
pub struct TransactionHistory {
    pub transaction_type: String, // "buy" or "sell"
    pub timestamp: u64, // ms since epoch
    pub amount: u64, // amount in SOL
    pub signature: String, // transaction signature
}

#[derive(Debug, Clone)]
pub struct BoughtTokenInfo {
    pub mint_key: String,
    pub bought_time: u64, // ms since epoch
    pub dev_sold_signature: String,
    pub status: String, // "monitored", "buy", "sell"
    pub dev_user_key: String, // dev user key as string
    pub is_prev_trade_sell: bool, // indicates if the previous trade was a sell
    pub prev_signature: String, // signature of the previous trade
    pub token_create_time: u64, // ms since epoch, default 0
    pub lastBuyTime: u64, // ms since epoch, default 0
    pub hasLargeBuy: bool, // true if a large buy has occurred, default false
    pub totalBuys: u64, // total number of buys, default 0
    pub bought_marketCap: u64, // market cap when token was bought, default 0
    pub transaction_history: Vec<TransactionHistory>, // array of buy/sell transactions
}



pub struct PumpBot {
    pub rpc_client: RpcClient,
    pub blockhash: AtomicPtr<Hash>,
    pub last_slot: AtomicU64,
    pub enabled: AtomicBool,
    pub monitored_new_tokens: Arc<Mutex<Vec<MonitoredToken>>>,
    pub monitored_dev_sold_tokens: Arc<Mutex<Vec<MonitoredToken>>>, // tokens to monitor for market cap alerts
    pub bought_history_tokens: Arc<Mutex<Vec<BoughtTokenInfo>>>, // stores all historical bought tokens (never removed)
    pub potential_tokens_step1: Arc<Mutex<Vec<MonitoredToken>>>, // stores info about potential tokens (now MonitoredToken)
    pub potential_tokens_step2: Arc<Mutex<Vec<MonitoredToken>>>, // stores info about potential tokens (step2)
    pub potential_tokens_step3: Arc<Mutex<Vec<MonitoredToken>>>, // stores info about potential tokens (step3)
    pub transaction_sender: tokio::sync::mpsc::UnboundedSender<(Pubkey, Pubkey, Hash, String, bool)>, // (mint_key, curve_key, blockhash, dev_user_key, is_buy) for transaction creation
    pub ws_service: Option<WebSocketService>,
    pub ws_rx: Option<broadcast::Receiver<String>>,
}
impl PumpBot {


    pub const PUMP_PID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
    pub const PUMP_MINT_INDEX: usize = 2;
    pub const PUMP_CURVE_INDEX: usize = 3;
    pub const PUMP_ATA_VAULT_INDEX: usize = 4;
    pub const PUMP_USER_INDEX: usize = 6;
    pub const AXIOS_PID: &str = "AxiomfHaWDemCFBLBayqnEnNwE6b7B2Qz3UmzMpgbMG6";
    pub const AXIOS_BUY_IX_DISCRIMINATOR: [u8; 8] = [0x00, 0x00, 0x71, 0xcf, 0x46, 0x00, 0x00, 0x00];
    pub const AXIOS_SELL_IX_DISCRIMINATOR: [u8; 8] = [0x5d, 0x58, 0x3c, 0x22, 0x5b, 0x12, 0x56, 0xc5];

    pub const BONK_PUMP_PID: &str = "LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj";
    pub const BONK_PUMP_PID1: &str = "FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1";
    pub const BONK_NEW_TOKEN_MINT_INDEX: usize = 6;
    pub const BONK_NEW_TOKEN_CURVE_INDEX: usize = 5;
    pub const BONK_NEW_TOKEN_USER_INDEX: usize = 0;
    pub const BONK_NEW_TOKEN_AMOUNT_POS: usize = 8;
    pub const BONK_NEW_TOKEN_ATA_VAULT_INDEX: usize = 9;
    //pub const BONK_LAUNCH_IX_DISCRIMINATOR: [u8; 8] = [0xfa, 0xea, 0x0d, 0x7b, 0xd5, 0x9c, 0x13, 0xec];
    pub const BONK_LAUNCH_IX_DISCRIMINATOR: [u8; 8] = [0xaf, 0xaf, 0x6d, 0x1f, 0x0d, 0x98, 0x9b, 0xed];
    pub const BONK_SELL_IX_DISCRIMINATOR: [u8; 8] = [0x95, 0x27, 0xde, 0x9b, 0xd3, 0x7c, 0x98, 0x1a];
    pub const BONK_BUY_IX_DISCRIMINATOR: [u8; 8] = [0xfa, 0xea, 0x0d, 0x7b, 0xd5, 0x9c, 0x13, 0xec];

    pub const BONK_MINT_INDEX: usize = 9;  
    pub const BONK_CURVE_INDEX: usize = 4;
    pub const BONK_USER_INDEX: usize = 0;
    pub const BONK_AMOUNT_POS1: usize = 8;
    pub const BONK_AMOUNT_POS2: usize = 16;
    pub const BONK_ATA_VAULT_INDEX: usize = 8;

    pub const PUMP_DEV_MINT_INDEX: usize = 0;
    pub const PUMP_DEV_CURVE_INDEX: usize = 2;
    pub const PUMP_DEV_USER_INDEX: usize = 7;
    pub const PUMP_AMOUNT_POS1: usize = 8;
    pub const PUMP_AMOUNT_POS2: usize = 16;
    pub const LAUNCH_IX_DISCRIMINATOR: [u8; 8] = [0x18, 0x1e, 0xc8, 0x28, 0x05, 0x1c, 0x07, 0x77];
    pub const BUY_IX_DISCRIMINATOR: [u8; 8] = [0x52, 0xe1, 0x77, 0xe7, 0x4e, 0x1d, 0x2d, 0x46];
    pub const SELL_IX_DISCRIMINATOR: [u8; 8] = [0x5d, 0x58, 0x3c, 0x22, 0x5b, 0x12, 0x56, 0xc5];

    // Alternative discriminators for backward compatibility
    pub const BUY_IX_DISCRIMINATOR_ALT: [u8; 8] = [0x66, 0x06, 0x3d, 0x12, 0x01, 0xda, 0xeb, 0xea];
    pub const SELL_IX_DISCRIMINATOR_ALT: [u8; 8] = [0x33, 0xe6, 0x85, 0xa4, 0x01, 0x7f, 0x83, 0xad];
    pub const BUY_IX_DISCRIMINATOR_ALT2: [u8; 8] = [0xfa, 0xea, 0x0d, 0x7b, 0xd5, 0x9c, 0x13, 0xec];
    pub const SELL_IX_DISCRIMINATOR_ALT2: [u8; 8] = [0x2c, 0x77, 0xaf, 0xda, 0xc7, 0x4d, 0xc4, 0xeb];
    pub const SELL_IX_DISCRIMINATOR_ALT3: [u8; 8] = [0x95, 0x27, 0xde, 0x9b, 0xd3, 0x7c, 0x98, 0x1a];
    
    pub const BLOOM_PID: &str = "b1oomGGqPKGD6errbyfbVMBuzSC8WtAAYo8MwNafWW1";
    pub const BLOOM_MINT_INDEX: usize = 11;
    pub const BLOOM_CURVE_INDEX: usize = 7;
    pub const BLOOM_ATA_VAULT_INDEX: usize = 12;
    pub const BLOOM_PUMP_IX_POS: usize = 53;
    pub const BLOOM_PUMP_AMOUNT_POS: usize = 61;

    pub const PHONTON_PID: &str = "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW";
    pub const PHONTON_MINT_INDEX: usize = 3;
    pub const PHONTON_CURVE_INDEX: usize = 4;
    pub const PHONTON_ATA_VAULT_INDEX: usize = 5;
    pub const PHONTON_PUMP_AMOUNT_POS: usize = 16;
    pub const PHONTON_BUY_IX_DISCRIMINATOR: [u8; 8] = [0x52, 0xe1, 0x77, 0xe7, 0x4e, 0x1d, 0x2d, 0x46];
    pub const PHONTON_SELL_IX_DISCRIMINATOR: [u8; 8] = [0x5d, 0x58, 0x3c, 0x22, 0x5b, 0x12, 0x56, 0xc5];
    
    pub const custom_PID: &str = "8LrRs65vWyoj9c38RE3Ne8AFHCeime2JxW5TrdoyS65s";

    pub const custom_BUY_MINT_INDEX: usize = 5;
    pub const custom_BUY_CURVE_INDEX: usize = 7;
    pub const custom_BUY_USER_INDEX: usize = 2;
    pub const custom_AMOUNT_POS1: usize = 8;
    pub const custom_AMOUNT_POS2: usize = 16;

    pub const custom_SELL_MINT_INDEX: usize = 0;
    pub const custom_SELL_CURVE_INDEX: usize = 9;
    pub const custom_SELL_USER_INDEX: usize = 1;
    pub const custom_BUY_IX_DISCRIMINATOR: [u8; 8] = [0x03, 0x2f, 0x84, 0xa2, 0xac, 0x90, 0xaf, 0x84];
    pub const custom_SELL_IX_DISCRIMINATOR: [u8; 8] = [0xa4, 0x95, 0xf7, 0x89, 0x6d, 0x31, 0xc5, 0x6d];

    pub const SHREDSTREAM_ENDPOINT: &str = "http://127.0.0.1:9900";
    

    pub fn create() -> Self {
        dotenv().ok();
        let SOLTRACKER_WSS_URL = std::env::var("SOLTRACKER_WSS_URL").unwrap_or_default();
        let HELIUS_RPC_URL = std::env::var("HELIUS_RPC_URL").unwrap_or_default();

        let rpc_client = RpcClient::new_with_commitment(
            String::from(HELIUS_RPC_URL),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();//rpc_client.get_latest_blockhash().expect("error getting blockhash");
        
        // Create channel for transaction requests
        let (transaction_sender, transaction_receiver) = tokio::sync::mpsc::unbounded_channel::<(Pubkey, Pubkey, Hash, String, bool)>();
        let client = reqwest::Client::new();
        
        // Read config.txt to check REAL_BUY setting
        let real_buy_enabled = Self::read_real_buy_config();
        
        // Start the transaction monitoring thread only if REAL_BUY=1
        if real_buy_enabled {
            Self::start_transaction_monitor(transaction_receiver, client);
            println!("🔍 REAL_BUY is enabled. Transaction monitor thread started.");
        } else {
            println!("🔍 REAL_BUY is disabled. Transaction monitor thread not started.");
        }

        let ws_service = WebSocketService::new(&SOLTRACKER_WSS_URL).ok();
        let ws_rx = ws_service.as_ref().map(|ws| ws.get_message_receiver().resubscribe());

        let room = "latest"; // new token room
        ws_service.as_ref().expect("WebSocketService not initialized").join_room(room);

        Self {
            rpc_client,
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            monitored_new_tokens: Arc::new(Mutex::new(Vec::new())),
            monitored_dev_sold_tokens: Arc::new(Mutex::new(Vec::new())),
            bought_history_tokens: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step1: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step2: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step3: Arc::new(Mutex::new(Vec::new())),
            transaction_sender,
            ws_service,
            ws_rx,
        }
    }

    fn read_real_buy_config() -> bool {
        match std::fs::read_to_string("config.txt") {
            Ok(contents) => {
                for line in contents.lines() {
                    let line = line.trim();
                    if line.starts_with("REAL_BUY=") {
                        let value = line.split('=').nth(1).unwrap_or("0").trim();
                        return value == "1";
                    }
                }
                println!("🔍 REAL_BUY not found in config.txt, defaulting to disabled");
                false
            }
            Err(e) => {
                println!("🔍 Could not read config.txt: {}. REAL_BUY defaulting to disabled", e);
                false
            }
        }
    }
    pub fn send_tx(&self, tx: &VersionedTransaction, skip_preflight: bool) {
        self.rpc_client.send_transaction_with_config(tx, RpcSendTransactionConfig {
            skip_preflight,
            ..RpcSendTransactionConfig::default()
        }).expect("sending tx error");
    }


    pub fn parse_time_string(time_str: &str) -> Option<chrono::DateTime<chrono::Utc>> {
        // Try parsing format: "YYYY-MM-DD HH:MM:SS:mmm" (with milliseconds)
        if let Ok(datetime) = chrono::NaiveDateTime::parse_from_str(time_str, "%Y-%m-%d %H:%M:%S:%3f") {
            Some(chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(datetime, chrono::Utc))
        } else {
            // Try parsing format: "YYYY-MM-DD HH:MM:SS" (without milliseconds)
            if let Ok(datetime) = chrono::NaiveDateTime::parse_from_str(time_str, "%Y-%m-%d %H:%M:%S") {
                Some(chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(datetime, chrono::Utc))
            } else {
                None
            }
        }
    }

    pub fn is_token_older_than_minutes(token_time: &str, minutes: u64) -> bool {
        if let Some(token_datetime) = Self::parse_time_string(token_time) {
            let current_time = chrono::Utc::now();
            let duration = current_time.signed_duration_since(token_datetime);
            duration.num_minutes() >= minutes as i64
        } else {
            false // If we can't parse the time, assume it's not old enough to remove
        }
    }

    pub fn is_token_older_than_seconds(token_time: u64, seconds: u64) -> bool {
        let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
        current_time_ms - token_time >= seconds as u64 * 1000
    }
    pub fn add_monitored_token(&self, mint_key: String, user_key: String, curve: String, slot: u64, token_create_signature: String, created_at: u64) {
        let monitored_token = MonitoredToken {
            mint_key: mint_key.to_string(),
            dev_user_key: user_key.to_string(),
            curve: curve.to_string(),
            detected_at_slot: slot,
            token_create_time: created_at,
            token_create_signature,
            dev_initial_buy_amount: None,
            dev_sell_amount: 0, // Initialize to 0
            dev_sold_time: None,
            twitterUrl: "".to_string(),
            totalBuys: 0, // total number of buys
            lastBuyTime: 0, // timestamp of the last buy
            hasLargeBuy: false, // true if a large buy has occurred, default false
            errorCount: 0, // number of errors encountered, default 0
            tokenSupply: 0, // total supply, initial value 0
        };
        
        if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
            // Clean up tokens older than 20 minutes
            let initial_count = tokens.len();
            tokens.retain(|token| !Self::is_token_older_than_seconds(token.token_create_time, 20 * 60)); // clear new token older than 20 minutes
            
            // Check if token already exists
            let exists = tokens.iter().any(|token| token.mint_key == monitored_token.mint_key);
            if !exists {
                let message = format!(
                    "🚀 New token detected at slot {} | 🪙 Mint: {} | 👤 Dev: {} | 📈 Curve: {} | 🕐 {}",
                    slot, monitored_token.mint_key.clone(), monitored_token.dev_user_key.clone(), monitored_token.curve.clone(), created_at
                );
                //println!("{}", message);
                self.write_alert_to_file(&message, "step0_new_token_alert.txt");
                tokens.push(monitored_token.clone());
            }
        }
    }
    pub fn get_monitored_tokens(&self) -> Vec<MonitoredToken> {
        if let Ok(tokens) = self.monitored_new_tokens.lock() {
            tokens.clone()
        } else {
            Vec::new()
        }
    }

    pub async fn process_entries(&self, entries: Vec<Entry>, slot: u64, start: u32, end: u32) -> bool {
        if self.enabled.load(Ordering::SeqCst) {
            let cur_slot = self.last_slot.load(Ordering::SeqCst);
            if cur_slot == 0 {
                self.update_blockhash(slot);
                return false;
            }

            let pump_pid = Pubkey::from_str(Self::PUMP_PID).unwrap();
            let axios_pid = Pubkey::from_str(Self::AXIOS_PID).unwrap();
            let bonk_pump_pid = Pubkey::from_str(Self::BONK_PUMP_PID).unwrap();
            let bonk_pump_pid1 = Pubkey::from_str(Self::BONK_PUMP_PID1).unwrap();
            let custom_pid = Pubkey::from_str(Self::custom_PID).unwrap();

            for entry in entries.iter() {
                for tx in entry.transactions.iter() {
                    let static_keys = tx.message.static_account_keys();

                    if static_keys.contains(&pump_pid) || static_keys.contains(&bonk_pump_pid)
                        || static_keys.contains(&bonk_pump_pid1) || static_keys.contains(&axios_pid)
                        || static_keys.contains(&custom_pid)
                    {
                        let signature = tx.signatures.get(0).cloned().unwrap_or_default();
                        let sig_message = format!("{} | {}  ", Self::get_cur_time_ms(), signature);
                        self.write_alert_to_file(&sig_message, "sig_message.txt");
                        //println!("{}", sig_message);
                        for ix in tx.message.instructions() {
                            let ix_pid: &Pubkey = &static_keys[ix.program_id_index as usize];

                            let mut is_new_token = false;
                            let mut is_buy = false;
                            let mut is_sell = false;
                            let mut mint_key: Option<&Pubkey> = None;
                            let mut user_key: Option<&Pubkey> = None;
                            let mut curve: Option<&Pubkey> = None;
                            let mut amount_sol: Option<u64> = None;
                            let mut associated_vault: Option<&Pubkey> = None;

                            if static_keys.contains(&custom_pid) {
                                is_sell = ix.data.starts_with(&Self::custom_SELL_IX_DISCRIMINATOR);
                                is_buy = ix.data.starts_with(&Self::custom_BUY_IX_DISCRIMINATOR);

                                is_new_token = false;

                                if is_sell {
                                    if !self.validate_initial_data_size(ix, static_keys, &[Self::custom_SELL_MINT_INDEX, Self::custom_SELL_USER_INDEX, Self::custom_SELL_CURVE_INDEX]) {
                                        continue;
                                    }
                                    mint_key = Some(&static_keys[ix.accounts[Self::custom_SELL_MINT_INDEX] as usize]);
                                    user_key = Some(&static_keys[ix.accounts[Self::custom_SELL_USER_INDEX] as usize]);
                                    curve = Some(&static_keys[ix.accounts[Self::custom_SELL_CURVE_INDEX] as usize]);
                                    //associated_vault = Some(&static_keys[ix.accounts[Self::custom_SELL_ATA_VAULT_INDEX] as usize]);
                                }
                                else if is_buy {
                                    if !self.validate_initial_data_size(ix, static_keys, &[Self::custom_BUY_MINT_INDEX, Self::custom_BUY_USER_INDEX, Self::custom_BUY_CURVE_INDEX]) {
                                        continue;
                                    }
                                    mint_key = Some(&static_keys[ix.accounts[Self::custom_BUY_MINT_INDEX] as usize]);
                                    user_key = Some(&static_keys[ix.accounts[Self::custom_BUY_USER_INDEX] as usize]);
                                    curve = Some(&static_keys[ix.accounts[Self::custom_BUY_CURVE_INDEX] as usize]);
                                    //associated_vault = Some(&static_keys[ix.accounts[Self::custom_BUY_ATA_VAULT_INDEX] as usize]);
                                }

                                // Use enhanced validation for amount extraction
                                amount_sol = self.validate_and_extract_amount(ix, Self::custom_AMOUNT_POS1, Some(Self::custom_AMOUNT_POS2), &signature.to_string());

                                // Skip if amount extraction failed
                                if amount_sol.is_none() {
                                    continue;
                                }

                                println!("**custom_pid mint_key: {:?}  user_key: {:?}  curve: {:?}  signature: {:?} amount_sol: {:?} is_sell: {:?} is_buy: {:?} is_new_token: {:?}", mint_key, user_key, curve, signature, amount_sol, is_sell, is_buy, is_new_token);
                            }
                            else if static_keys.contains(&bonk_pump_pid) || static_keys.contains(&bonk_pump_pid1){
                                is_new_token = ix.data.starts_with(&Self::BONK_LAUNCH_IX_DISCRIMINATOR);
                                is_sell = ix.data.starts_with(&Self::BONK_SELL_IX_DISCRIMINATOR);
                                is_buy = ix.data.starts_with(&Self::BONK_BUY_IX_DISCRIMINATOR);
                                
                                if is_new_token {
                                    if !self.validate_initial_data_size(ix, static_keys, &[Self::BONK_NEW_TOKEN_MINT_INDEX, Self::BONK_NEW_TOKEN_USER_INDEX, Self::BONK_NEW_TOKEN_CURVE_INDEX]) {
                                        continue;
                                    }
                                    mint_key = Some(&static_keys[ix.accounts[Self::BONK_NEW_TOKEN_MINT_INDEX] as usize]);
                                    user_key = Some(&static_keys[ix.accounts[Self::BONK_NEW_TOKEN_USER_INDEX] as usize]);
                                    curve = Some(&static_keys[ix.accounts[Self::BONK_NEW_TOKEN_CURVE_INDEX] as usize]);
                                    //associated_vault = Some(&static_keys[ix.accounts[Self::BONK_NEW_TOKEN_ATA_VAULT_INDEX] as usize]);
                                    // Use enhanced validation for amount extraction
                                    amount_sol = self.validate_and_extract_amount(ix, Self::BONK_NEW_TOKEN_AMOUNT_POS, None, &signature.to_string());
                                
                                }
                                else {

                                    if !self.validate_initial_data_size(ix, static_keys, &[Self::BONK_MINT_INDEX, Self::BONK_USER_INDEX, Self::BONK_CURVE_INDEX]) {
                                        continue;
                                    }

                                    mint_key = Some(&static_keys[ix.accounts[Self::BONK_MINT_INDEX] as usize]);
                                    user_key = Some(&static_keys[ix.accounts[Self::BONK_USER_INDEX] as usize]);
                                    curve = Some(&static_keys[ix.accounts[Self::BONK_CURVE_INDEX] as usize]);
                                    //associated_vault = Some(&static_keys[ix.accounts[Self::BONK_ATA_VAULT_INDEX] as usize]);
                                    //println!("mint_key: {:?}  user_key: {:?}  curve: {:?}  signature: {:?}", mint_key, user_key, curve, signature);
                                    // Use enhanced validation for amount extraction with fallback logic
                                    if ix.data.len() >= 24 {
                                        amount_sol = self.validate_and_extract_amount(ix, Self::BONK_AMOUNT_POS1, Some(Self::BONK_AMOUNT_POS2), &signature.to_string());
                                    } else if ix.data.len() >= 16 {
                                        // Fallback to single amount if only 8 bytes available
                                        amount_sol = self.validate_and_extract_amount(ix, Self::BONK_AMOUNT_POS1, None, &signature.to_string());
                                    } else {
                                        let error_msg = format!("🔍 BONK Amount Extraction Error: Insufficient data length {} for signature {}", ix.data.len(), signature);
                                        self.write_alert_to_file(&error_msg, "validation_errors.txt");
                                        continue;
                                    }

                                    // Skip if amount extraction failed
                                    if amount_sol.is_none() {
                                        continue;
                                    }
                                }

                            }
                            else if static_keys.contains(&pump_pid) || static_keys.contains(&axios_pid) {
                                is_new_token = ix.data.starts_with(&Self::LAUNCH_IX_DISCRIMINATOR);

                                is_buy = ix.data.starts_with(&Self::BUY_IX_DISCRIMINATOR) ||
                                    ix.data.starts_with(&Self::BUY_IX_DISCRIMINATOR_ALT) ||
                                    ix.data.starts_with(&Self::BUY_IX_DISCRIMINATOR_ALT2);

                                is_sell = ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR) ||
                                        ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR_ALT) ||
                                        ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR_ALT2) ||
                                        ix.data.starts_with(&Self::SELL_IX_DISCRIMINATOR_ALT3);

                                if static_keys.contains(&axios_pid) {
                                    is_buy = true;
                                    is_sell = false;
                                    is_new_token = false;
                                }

                                if is_new_token {
                                    if !self.validate_initial_data_size(ix, static_keys, &[Self::PUMP_DEV_MINT_INDEX, Self::PUMP_DEV_USER_INDEX, Self::PUMP_DEV_CURVE_INDEX]) {
                                        continue;
                                    }
                                    mint_key = Some(&static_keys[ix.accounts[Self::PUMP_DEV_MINT_INDEX] as usize]);
                                    user_key = Some(&static_keys[ix.accounts[Self::PUMP_DEV_USER_INDEX] as usize]);
                                    curve = Some(&static_keys[ix.accounts[Self::PUMP_DEV_CURVE_INDEX] as usize]);
                                    //associated_vault = Some(&static_keys[ix.accounts[Self::PUMP_ATA_VAULT_INDEX] as usize]);
                                }
                                else {
                                    if !self.validate_initial_data_size(ix, static_keys, &[Self::PUMP_MINT_INDEX, Self::PUMP_USER_INDEX, Self::PUMP_CURVE_INDEX]) {
                                        continue;
                                    }
                                    mint_key = Some(&static_keys[ix.accounts[Self::PUMP_MINT_INDEX] as usize]);
                                    user_key = Some(&static_keys[ix.accounts[Self::PUMP_USER_INDEX] as usize]);
                                    curve = Some(&static_keys[ix.accounts[Self::PUMP_CURVE_INDEX] as usize]);
                                    //associated_vault = Some(&static_keys[ix.accounts[Self::PUMP_ATA_VAULT_INDEX] as usize]);
                                }
                                
                                // Use enhanced validation for amount extraction with fallback logic
                                if ix.data.len() >= 24 {
                                    amount_sol = self.validate_and_extract_amount(ix, Self::PUMP_AMOUNT_POS1, Some(Self::PUMP_AMOUNT_POS2), &signature.to_string());
                                } else if ix.data.len() >= 16 {
                                    // Fallback to single amount if only 8 bytes available
                                    amount_sol = self.validate_and_extract_amount(ix, Self::PUMP_AMOUNT_POS1, None, &signature.to_string());
                                } else {
                                    let error_msg = format!("🔍 PUMP Amount Extraction Error: Insufficient data length {} for signature {}", ix.data.len(), signature);
                                    self.write_alert_to_file(&error_msg, "validation_errors.txt");
                                    continue;
                                }

                                // Skip if amount extraction failed
                                if amount_sol.is_none() {
                                    continue;
                                }

                                /*if !is_sell && !is_buy && !is_new_token {
                                    println!("**pump_pid mint_key: {:?}  user_key: {:?}  curve: {:?}  signature: {:?} amount_sol: {:?} is_sell: {:?} is_buy: {:?} is_new_token: {:?}", mint_key, user_key, curve, signature, amount_sol, is_sell, is_buy, is_new_token);
                                }*/
                                
                            }

                            // extract basic data
                            if is_buy  {
                                // Add buy transaction to history
                                if let (Some(mint_key_val), Some(amount_sol_val), Some(user_key_val)) = (mint_key, amount_sol, user_key) {
                                    self.add_transaction_to_history(&mint_key_val.to_string(), "buy", amount_sol_val, &signature.to_string());
                                }

                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////
                                // find out potential tokens step1 logic ///////////////////////////////
                                if let Ok(mut dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
                                    for token in dev_sold_tokens.iter_mut() {
                                        if let (Some(mint_key_val), Some(amount_sol_val)) = (mint_key, amount_sol) {
                                            if token.mint_key.to_string() == mint_key_val.to_string() {
                                                // totalBuys and lastBuyTime logic as previous
                                                if amount_sol_val > 14000000000000 {
                                                    token.hasLargeBuy = true;
                                                }

                                                let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                                                if token.totalBuys > 0 {
                                                    // check lastBuyTime is less than 2 secs
                                                    if current_time_ms - token.lastBuyTime >= 2000 {
                                                        token.totalBuys = 0;
                                                        token.hasLargeBuy = false;
                                                    }
                                                }
                                                token.totalBuys += 1;
                                                token.lastBuyTime = current_time_ms;
                                                
                                                if token.hasLargeBuy && token.totalBuys >= 3 {

                                                    if let Ok(mut potential_tokens_step1) = self.potential_tokens_step1.lock() {
                                                        let potential_token_info = MonitoredToken {
                                                            mint_key: mint_key_val.to_string(),
                                                            dev_user_key: token.dev_user_key.clone(),
                                                            curve: token.curve.clone(),
                                                            detected_at_slot: token.detected_at_slot,
                                                            token_create_signature: token.token_create_signature.clone(),
                                                            token_create_time: token.token_create_time,
                                                            dev_initial_buy_amount: None,
                                                            dev_sell_amount: 0,
                                                            dev_sold_time: None,
                                                            twitterUrl: "".to_string(),
                                                            totalBuys: 0,
                                                            lastBuyTime: 0,
                                                            hasLargeBuy: false,
                                                            errorCount: 0, // number of errors encountered, default 0
                                                            tokenSupply: 0, // total supply, initial value 0
                                                        };
                                                        
                                                        // insert if there is no exist one

                                                        if !potential_tokens_step1.iter().any(|t| t.mint_key == mint_key_val.to_string()) {
                                                            potential_tokens_step1.push(potential_token_info.clone());
                                                            let message = format!("🔍 {} | 🔴 Potential Tokens Alert! | mint_key: {} | follower_amount_sol: {} | signature: {}", Self::get_cur_time_ms(), mint_key_val, amount_sol_val, signature);
                                                            self.write_alert_to_file(&message, "potential_tokens_step1.txt");
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////
                                // iterate potential_tokens_step2 and check if amount sol is over 1400000 ///////////////////////////////
                                let mut potential_bought_token_info = None;
                                if let Ok(mut potential_tokens_step3) = self.potential_tokens_step3.lock() {
                                    for token in potential_tokens_step3.iter_mut() {
                                        if let (Some(mint_key_val), Some(amount_sol_val)) = (mint_key, amount_sol) {
                                            if token.mint_key.to_string() == mint_key_val.to_string() {
                                                let current_time_ms_u64 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                                                let token_age = (current_time_ms_u64 - token.token_create_time) / 1000;

                                                if amount_sol_val > 5000000000000 {
                                                    token.hasLargeBuy = true;
                                                }

                                                if token.totalBuys > 0 {
                                                    // check lastBuyTime is less than 2 secs
                                                    if current_time_ms_u64 - token.lastBuyTime >= 2000 {
                                                        token.totalBuys = 0;
                                                        token.hasLargeBuy = false;
                                                    }
                                                }
                                                token.totalBuys += 1;
                                                token.lastBuyTime = current_time_ms_u64;
                                              
                                                if token.hasLargeBuy {
                                                    //let msg = format!("🔍 DEBUG: potential_tokens_step2 length: {}", potential_tokens_step2.len());
                                                    //println!("{}", msg);
                                                    //self.write_alert_to_file(&msg, "potential_tokens_step2.txt");

                                                    let should_alert = {
                                                        if let Ok(mut bought_history_tokens) = self.bought_history_tokens.lock() {
                                                            if bought_history_tokens.iter().any(|t| t.mint_key == mint_key_val.to_string()) {
                                                                false
                                                            } else {
                                                                // Add to both bought_tokens (for tracking current positions) and bought_history_tokens (for duplicate prevention)
                                                                let bought_token_info = BoughtTokenInfo {
                                                                    mint_key: mint_key_val.to_string(),
                                                                    dev_sold_signature: signature.to_string(),
                                                                    bought_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64,
                                                                    status: "monitored".to_string(),
                                                                    dev_user_key: token.dev_user_key.clone(),
                                                                    is_prev_trade_sell: false, // Initialize to false
                                                                    prev_signature: "".to_string(), // Initialize with current signature
                                                                    token_create_time: token.token_create_time, // ms since epoch, default 0
                                                                    lastBuyTime: 0, // ms since epoch, default 0
                                                                    hasLargeBuy: false, // true if a large buy has occurred, default false
                                                                    totalBuys: 0, // total number of buys, default 0
                                                                    bought_marketCap: 0, // market cap when token was bought, default 0
                                                                    transaction_history: Vec::new(), // initialize empty transaction history
                                                                };
                                                                
                                                                // Add to history (never removed)
                                                                bought_history_tokens.push(bought_token_info);

                                                                true
                                                            }
                                                        } else {
                                                            println!("🔍 DEBUG: Failed to lock bought_history_tokens for token {}", mint_key_val);
                                                            true // If we can't lock, alert anyway
                                                        }
                                                    };
                                                    
                                                    if should_alert  {
                                                        
                                                        let alert_message = format!("🚨 {} | 🔴 Bought Tokens Alert! | mint_key: {} | dev_user_key: {} | follower_amount_sol: {} | follower_signature: {} | token_age: {}s", Self::get_cur_time_ms(), mint_key_val, token.dev_user_key, amount_sol_val, signature, token_age);
                                                        // Print to console
                                                        println!("{}", alert_message);
                                                        println!("**************************************************");

                                                        // third 1 means continuous buy
                                                        //let alert_message1 = format!("{} | {} | 0 | {} | age: {}s", Self::get_cur_time_ms(), mint_key_val, signature, token_age);
                                                        
                                                        self.write_alert_to_file(&alert_message, "bought_tokens.txt");

                                                        let current_blockhash = unsafe{ &* self.blockhash.load(Ordering::SeqCst)};
                                                        // Send transaction request to monitoring thread with dev_user_key
                                                        if let (Some(mint_key_val), Some(curve_val)) = (mint_key, curve) {
                                                            if let Err(e) = self.transaction_sender.send((mint_key_val.clone(), curve_val.clone(), *current_blockhash, token.dev_user_key.clone(), true)) {
                                                                println!("❌ Failed to send transaction request: {}", e);
                                                            } else {
                                                                println!("📤 Sent transaction request for mint: {} and curve: {} with blockhash: {} and dev_user_key: {} (is_buy: true)", mint_key_val, curve_val, current_blockhash, token.dev_user_key);
                                                            }
                                                        }

                                                        potential_bought_token_info = Some(token.clone());
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                                // remove potential_bought_token_info from potential_tokens_step3
                                if let Some(token) = potential_bought_token_info {
                                    if let Ok(mut potential_tokens_step3) = self.potential_tokens_step3.lock() {
                                        potential_tokens_step3.retain(|t| t.mint_key != token.mint_key);
                                    }

                                    if let Ok(mut potential_tokens_step2) = self.potential_tokens_step2.lock() {
                                        potential_tokens_step2.retain(|t| t.mint_key != token.mint_key);
                                    }

                                    if let Ok(mut potential_tokens_step1) = self.potential_tokens_step1.lock() {
                                        potential_tokens_step1.retain(|t| t.mint_key != token.mint_key);
                                    }

                                    if let Ok(mut monitored_dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
                                        monitored_dev_sold_tokens.retain(|t| t.mint_key != token.mint_key);
                                    }
                                }
                                
                            }
                            if is_sell {
                                
                                let trade_history_message = format!("{} | mint_key: {:?} | user_key: {:?} | amount_sol: {:?} | signature: {:?}", Self::get_cur_time_ms(), mint_key, user_key, amount_sol, signature);
                                self.write_alert_to_file(&trade_history_message, "sell_trade_history.txt");
                                
                                // Add sell transaction to history
                                if let (Some(mint_key_val), Some(amount_sol_val)) = (mint_key, amount_sol) {
                                    self.add_transaction_to_history(&mint_key_val.to_string(), "sell", amount_sol_val, &signature.to_string());
                                }
                                
                                // iterate monitored_dev_sold_tokens and check if mint_key is in the list
                                
                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////
                                //// implement stop loss logic ////
                                //////////////////////////////////////////////////////////////
                                
                                if let Ok(mut bought_history_tokens) = self.bought_history_tokens.lock() {
                                    
                                    for token in bought_history_tokens.iter() {
                                        if let (Some(mint_key_val), Some(amount_sol_val)) = (mint_key, amount_sol) {
                                            if token.mint_key.to_string() == mint_key_val.to_string() {

                                                let transaction_history = token.transaction_history.clone();
                                                let current_time_ms_u64 = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                                                // iterate transaction_history and check if there is a sell transaction
                                                let prev_time_5_secs = current_time_ms_u64 - 5000;
                                                let prev_time_4_secs = current_time_ms_u64 - 4000;

                                                let mut total_sell_amount = 0;
                                                let mut total_buy_amount = 0;

                                                let mut total_sell_amount_4_secs = 0;
                                                let mut total_buy_amount_4_secs = 0;
                                                for transaction in transaction_history.iter() {

                                                    if transaction.timestamp > prev_time_5_secs && transaction.timestamp < current_time_ms_u64 {
                                                        if transaction.transaction_type == "sell" {
                                                            total_sell_amount += transaction.amount;
                                                        }
                                                        else {
                                                            total_buy_amount += transaction.amount;
                                                        }
                                                    }
                                                    if transaction.timestamp > prev_time_4_secs && transaction.timestamp < current_time_ms_u64 {
                                                        if transaction.transaction_type == "sell" {
                                                            total_sell_amount_4_secs += transaction.amount;
                                                        }
                                                        else {
                                                            total_buy_amount_4_secs += transaction.amount;
                                                        }
                                                    }
                                                }

                                                let msg = format!("🔍 {} | DEBUG: Stop Loss History - mint_key: {} | total_sell_amount: {} | total_buy_amount: {}", Self::get_cur_time_ms(), mint_key_val, total_sell_amount, total_buy_amount);
                                                self.write_alert_to_file(&msg, "stop_loss_history_5_secs.txt");

                                                let msg_4_secs = format!("🔍 {} | DEBUG: Stop Loss History - mint_key: {} | total_sell_amount_4_secs: {} | total_buy_amount_4_secs: {}", Self::get_cur_time_ms(), mint_key_val, total_sell_amount_4_secs, total_buy_amount_4_secs);
                                                self.write_alert_to_file(&msg_4_secs, "stop_loss_history_4_secs.txt");
                                                
                                                if total_sell_amount > total_buy_amount {
                                                    let msg = format!("🔍 {} | Sell Alert: 5 secs Stop Loss History - mint_key: {} | total_sell_amount: {} | total_buy_amount: {}", Self::get_cur_time_ms(), mint_key_val, total_sell_amount, total_buy_amount);
                                                    self.write_alert_to_file(&msg, "sell_tokens.txt");
                                                }
                                                if total_sell_amount_4_secs > total_buy_amount_4_secs {
                                                    let msg = format!("�� {} | Sell Alert: 4 secs Stop Loss History - mint_key: {} | total_sell_amount_4_secs: {} | total_buy_amount_4_secs: {}", Self::get_cur_time_ms(), mint_key_val, total_sell_amount_4_secs, total_buy_amount_4_secs);
                                                    self.write_alert_to_file(&msg, "sell_tokens.txt");
                                                }
                                                // check if amount_sol is greater than 20M tokens
                                                //println!("mint_key_val: {}, amount_sol_val: {}, is_auto_sell: {}", mint_key_val.to_string(), amount_sol_val, self.is_auto_sell(&mint_key_val.to_string()));
                                               /* if amount_sol_val > 17000000000000 && self.is_auto_sell(&mint_key_val.to_string()) {
                                                    let alert_message = format!("🚨 {} | 🔴  ** Sold Tokens Alert ** | mint_key: {} | follower_amount_sol: {} | signature: {} | prev_signature: {} | is_prev_trade_sell: {}", Self::get_cur_time_ms(), mint_key_val, amount_sol_val, signature, token.prev_signature, token.is_prev_trade_sell  );
                                                    println!("{}", alert_message);

                                                    //self.write_sell_alert_to_file(&alert_message);
                                                    let current_blockhash = unsafe{ &* self.blockhash.load(Ordering::SeqCst)};
                                                    // implement self.transaction_sender.send here to sell all tokens
                                                    if let Err(e) = self.transaction_sender.send((mint_key_val.clone(), curve.unwrap_or(&Pubkey::default()).clone(), *current_blockhash, Pubkey::from_str(&token.dev_user_key).unwrap_or(Pubkey::default()), false)) {
                                                        println!("❌ Failed to sell transaction request: {}", e);
                                                    } else {
                                                        println!("📤 Sent sell transaction request for mint: {}  with blockhash: {} and dev_user_key: {} (is_buy: false)", mint_key_val,  *current_blockhash, token.dev_user_key);
                                                    }

                                                    // Mark token for removal
                                                    token_to_remove = Some(token.clone());

                                                    //self.write_sell_alert_to_file(&alert_message);
                                                    break;
                                                }*/

                                            }
                                        }
                                    }
                                    
                                }


                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////
                                //// Check if this is a dev selling a monitored token ////
                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////
                                
                                let mut dev_sell_data = None;
                                let mut new_token_to_remove_pos = 0;
                                if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
                                    for (i, token) in tokens.iter().enumerate() {
                                        if let (Some(mint_key_val), Some(user_key_val)) = (mint_key, user_key) {
                                            let mint_match = token.mint_key == mint_key_val.to_string();
                                            let user_match = token.dev_user_key == user_key_val.to_string();
                                            
                                            if mint_match && user_match {
                                                let current_time = Self::get_cur_time_ms();
                                                let mint_address = mint_key_val.to_string();
                                                let curve = tokens[i].curve.clone();
                                                let token_create_time = tokens[i].token_create_time;
                                                let dev_initial_buy_amount = tokens[i].dev_initial_buy_amount.clone();
                                                let token_create_signature = tokens[i].token_create_signature.clone();
                                                let dev_sell_amount = tokens[i].dev_sell_amount.clone();
                                                // Update the token's sold time
                                                tokens[i].dev_sold_time = Some(current_time.clone());
                                                
                                                // Collect data for async processing
                                                dev_sell_data = Some((
                                                    mint_address,
                                                    token_create_time,
                                                    current_time,
                                                    mint_key_val.clone(),
                                                    user_key_val.clone(),
                                                    curve.to_string(),
                                                    amount_sol.unwrap_or(0),
                                                    signature.clone(),
                                                    slot,
                                                    dev_initial_buy_amount,
                                                    token_create_signature,
                                                    dev_sell_amount
                                                ));
                                                
                                                new_token_to_remove_pos = i;
                                                break;
                                            }
                                        }
                                    }
                                }
                                // logic: if dev_sell_amount is greater than 90% of dev_initial_buy_amount, then its dev sell alert
                                if let Some((mint_address, token_create_time, current_time, mint_key, user_key, curve, amount_sol, signature, slot, dev_initial_buy_amount, token_create_signature, dev_sell_amount)) = dev_sell_data {
                                    // Get risk data and check for Twitter URL
                                                            
                                    let is_monitored_dev_sold_token = {
                                        if let Ok(dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
                                            
                                            // Check if this mint is already in the dev sold tokens list
                                            let already_exists = dev_sold_tokens.iter().any(|t| {
                                                t.mint_key == mint_key.to_string()
                                            });
                                            
                                            already_exists
                                        } else {
                                            true // If we can't lock, broadcast anyway
                                        }
                                    };
                                    if !is_monitored_dev_sold_token {
                                        // compare dev_initial_buy_amount with amount_sol
                                        if let Some(dev_initial_buy_amount) = dev_initial_buy_amount {
                                                let total_dev_sold_amount = dev_sell_amount + amount_sol;
                                                let amount_percentage = (total_dev_sold_amount as f64 / dev_initial_buy_amount as f64) * 100.0;

                                                if amount_percentage >= 90.0 {
                                                    // Add to monitored dev sold tokens for market cap monitoring
                                                    let dev_sold_token = MonitoredToken {
                                                        mint_key: mint_key.to_string(),
                                                        dev_user_key: user_key.to_string(),
                                                        curve: curve.to_string(),
                                                        detected_at_slot: slot,
                                                        token_create_signature: token_create_signature.clone(),
                                                        token_create_time: token_create_time,
                                                        dev_initial_buy_amount: Some(dev_initial_buy_amount), // We don't have the original dev amount here
                                                        dev_sell_amount: total_dev_sold_amount, // Store the dev sell amount
                                                        dev_sold_time: Some(current_time.clone()),
                                                        twitterUrl: "".to_string(),
                                                        totalBuys: 0, // total number of buys
                                                        lastBuyTime: 0, // timestamp of the last buy
                                                        hasLargeBuy: false, // true if a large buy has occurred, default false
                                                        errorCount: 0, // number of errors encountered, default 0
                                                        tokenSupply: 0, // total supply, initial value 0
                                                    };
                                                    
                                                    if let Ok(mut dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
                                                        // if there is no exist token, then push it
                                                        if !dev_sold_tokens.iter().any(|t| t.mint_key == mint_key.to_string()) {
                                                            dev_sold_tokens.push(dev_sold_token);
                                                        }
                                                    }

                                                    if let Ok(mut new_tokens) = self.monitored_new_tokens.lock() {
                                                        new_tokens.remove(new_token_to_remove_pos);
                                                    }
                                                    
                                                    let alert_message = format!("🚨 {} | 🔴 DEV SELL DETECTED! | 🪙 Mint: {} | Amount Percentage: {} | 💰 Dev Created Amount: {} | 💰 Dev Sold Amount: {} | 📝 Signature: {}",
                                                        current_time, mint_key, amount_percentage , dev_initial_buy_amount, total_dev_sold_amount, signature);
                                                    self.write_alert_to_file(&alert_message, "dev_sell_alert.txt");
                                                    println!("{}", alert_message);
                                                }
                                                else {
                                                   
                                                    // update dev_sell_amount in monitored_new_tokens
                                                    if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
                                                        for token in tokens.iter_mut() {
                                                            if token.mint_key == mint_key.to_string() {
                                                                token.dev_sell_amount = total_dev_sold_amount;
                                                                break;
                                                            }
                                                        }
                                                    }
                                                    // println!("🔍 [total dev_sell_amount] Updated for mint {}: {} SOL of initial dev sell amount {} SOL", mint_key, total_dev_sold_amount, dev_initial_buy_amount);
                                                }
                                        }
                                    }
                                                        
                                }
                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////
                                //////////////////////////////////////////////////////////////

                                
                                return true;
                            }
                            if is_new_token {
                                
                                let current_time = Self::get_cur_time_ms();
                                let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                                if let (Some(mint_key_val), Some(user_key_val), Some(curve_val)) = (mint_key, user_key, curve) {
                                    if mint_key_val.to_string().ends_with("bonk") {
                                        let amount_sol_str = amount_sol.map(|amt| amt.to_string()).unwrap_or_else(|| "N/A".to_string());
                                        
                                        // Add to monitored tokens
                                        self.add_monitored_token(
                                            mint_key_val.to_string(),
                                            user_key_val.to_string(),
                                            curve_val.to_string(),
                                            slot,
                                            signature.to_string(),
                                            current_time_ms
                                        );
                                    }
                                }
                            }

                            if !is_new_token && !is_buy && !is_sell {
                                let message = format!("🔍 {} | DEBUG: No action taken for slot {} | mint_key: {:?} | signature: {}",
                                    Self::get_cur_time_ms(), slot, mint_key, signature);
                                self.write_alert_to_file(&message, "no_action_taken.txt");
                            }
                        }
                    }
                    
                }
            }

            self.update_blockhash(slot);
        }
        return false;
    }

    pub fn update_blockhash(&self, slot: u64) {
        let cur_slot = self.last_slot.load(Ordering::SeqCst);
        if cur_slot + 4 < slot {
            let blockhash_new_res = self.rpc_client.get_latest_blockhash();
            if blockhash_new_res.is_ok() {
                let blockhash_new = blockhash_new_res.expect("error getting blockhash");

                self.blockhash.store(Box::into_raw(Box::new(blockhash_new)), Ordering::SeqCst);
                
                self.last_slot.store(slot, Ordering::SeqCst);
                // println!("updated blockhash {:#?}, slot {}", blockhash_new, slot);
            }
            
        }
    }
    pub fn get_cur_time_ms() -> String {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Time went backwards");

        let secs = now.as_secs();
        let millis = now.subsec_millis();
        let datetime = chrono::DateTime::from_timestamp(secs as i64, 0).unwrap();
        format!("{}:{:03}", datetime.format("%Y-%m-%d %H:%M:%S"), millis)
    }

    pub fn add_transaction_to_history(&self, mint_key: &str, transaction_type: &str, amount: u64, signature: &str) {
        let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
        
        // Add to bought_history_tokens history
        if let Ok(mut bought_history_tokens) = self.bought_history_tokens.lock() {
            if let Some(token) = bought_history_tokens.iter_mut().find(|t| t.mint_key == mint_key) {
                let transaction = TransactionHistory {
                    transaction_type: transaction_type.to_string(),
                    timestamp: current_time_ms,
                    amount,
                    signature: signature.to_string(),
                };
                token.transaction_history.push(transaction);
            }
        }
    }

    pub async fn receive_entry_update(bot_param: Arc<PumpBot>) {
        let bot = Arc::clone(&bot_param);
        std::thread::spawn(move || {
            let rt = Runtime::new().unwrap();
            let result = rt.block_on(
                PumpBot::run_entry_update(bot)
            );
        });
    }

    pub async fn data_stream_thread(&self, mut rx: broadcast::Receiver<String>) {
        println!("Data stream thread started");
        while let Ok(msg) = rx.recv().await {

            // Enhanced JSON parsing with error handling
            let json: serde_json::Value = match serde_json::from_str(&msg) {
                Ok(parsed) => parsed,
                Err(e) => {
                    let error_msg = format!("🔍 WebSocket JSON Parse Error: Failed to parse message: {} - Error: {}", msg, e);
                    self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                    continue;
                }
            };

            // Enhanced data validation with detailed error logging
            if json["data"].is_null() {
                let error_msg = format!("🔍 WebSocket Data Error: Missing 'data' field in message: {}", msg);
                self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                continue;
            }

            // Validate token field exists and is not null
            if json["data"]["token"].is_null() {
                let error_msg = format!("🔍 WebSocket Data Error: Missing 'token' field in data: {}", json["data"]);
                self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                continue;
            }

            // Validate pools field exists and is not null
            if json["data"]["pools"].is_null() {
                let error_msg = format!("🔍 WebSocket Data Error: Missing 'pools' field in data: {}", json["data"]);
                self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                continue;
            }

                // Enhanced data extraction with validation
                let mint_key = match json["data"]["token"]["mint"].as_str() {
                    Some(key) if !key.is_empty() => key,
                    _ => {
                        let error_msg = format!("🔍 WebSocket Data Error: Invalid or missing mint key in token data");
                        self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                        continue;
                    }
                };

                // Validate pools array exists and has at least one element
                let pools = match json["data"]["pools"].as_array() {
                    Some(pools_array) if !pools_array.is_empty() => pools_array,
                    _ => {
                        let error_msg = format!("🔍 WebSocket Data Error: Invalid or empty pools array for mint {}", mint_key);
                        self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                        continue;
                    }
                };

                let user_key = match pools[0]["deployer"].as_str() {
                    Some(key) if !key.is_empty() => key,
                    _ => {
                        let error_msg = format!("🔍 WebSocket Data Error: Invalid or missing deployer for mint {}", mint_key);
                        self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                        continue;
                    }
                };

                let curve = match pools[0]["poolId"].as_str() {
                    Some(id) if !id.is_empty() => id,
                    _ => {
                        let error_msg = format!("🔍 WebSocket Data Error: Invalid or missing poolId for mint {}", mint_key);
                        self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                        continue;
                    }
                };

                let created_at = pools[0]["createdAt"].as_u64().unwrap_or(0);

                println!("🔍 Data Stream: {} | mint_key: {} | user_key: {} | curve: {} | created_at: {}", Self::get_cur_time_ms(), mint_key, user_key, curve, created_at);

                // Enhanced Twitter URL extraction with error handling
                let mut twitter_url = json["data"]["token"]["twitter"].as_str().unwrap_or("");
                if twitter_url.is_empty() {
                    // check json["data"]["token"]["extensions"] is not None
                    if !json["data"]["token"]["extensions"].is_null() {
                        match json["data"]["token"]["extensions"].as_object() {
                            Some(extensions) => {
                                for (key, value) in extensions.iter() {
                                    if !value.is_null() && !value.as_str().unwrap_or("").is_empty() {
                                        twitter_url = value.as_str().unwrap_or("");
                                        break;
                                    }
                                }
                            },
                            None => {
                                let error_msg = format!("🔍 WebSocket Data Warning: Extensions field is not an object for mint {}", mint_key);
                                self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                            }
                        }
                    }
                }

                // Add monitored token with validated data
                self.add_monitored_token(
                    mint_key.to_string(),
                    user_key.to_string(),
                    curve.to_string(),
                    0,
                    "".to_string(),
                    created_at
                );

                if !twitter_url.is_empty() {
                    // Enhanced Twitter URL update with error handling
                    match self.monitored_new_tokens.lock() {
                        Ok(mut tokens) => {
                            if let Some(token) = tokens.iter_mut().find(|t| t.mint_key == mint_key) {
                                token.twitterUrl = twitter_url.to_string();
                                println!("🔍 Updated Twitter URL for mint {}: {}", mint_key, twitter_url);
                            } else {
                                let error_msg = format!("🔍 WebSocket Warning: Token {} not found in monitored tokens for Twitter URL update", mint_key);
                                self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                            }
                        },
                        Err(e) => {
                            let error_msg = format!("🔍 WebSocket Error: Failed to lock monitored tokens for Twitter URL update: {}", e);
                            self.write_alert_to_file(&error_msg, "websocket_errors.txt");
                        }
                    }
                }
            }
            else{
                let error_msg = format!("🔍 WebSocket Info: Received non-data message: {}", msg);
                self.write_alert_to_file(&error_msg, "websocket_info.txt");
            }
        }
    }
    pub async fn run_entry_update(bot: Arc<PumpBot>) -> Result<(), Box<dyn std::error::Error>> {
        println!("Program Started: {}", Self::get_cur_time_ms());
        // Use ws_service from bot
        let websocket_thread = Arc::clone(&bot);

        let mut last_ping = Instant::now();
        let ping_interval = Duration::from_secs(10); // 10 seconds

        tokio::spawn(async move {
            let ws_service = websocket_thread.ws_service.as_ref().expect("WebSocketService not initialized");
            let mut last_ping = Instant::now();
            loop {
                if last_ping.elapsed() >= ping_interval {
                    ws_service.send_ping();
                    println!("Sent ping to WebSocket server.");
                    last_ping = Instant::now();
                }
                websocket_thread.data_stream_thread(websocket_thread.ws_rx.as_ref().expect("WebSocketService not initialized").resubscribe()).await;
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        });

        // step1: check token info and update social links
        let bot_clone_2 = Arc::clone(&bot);
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                let _result = bot_clone_2.step1_token_info_check_thread().await;
            }
        });

        // Start periodic cleanup task (every 2 minutes)
        /*let bot_cleanup = Arc::clone(&bot);
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(tokio::time::Duration::from_secs(1800)).await; // 2 minutes
                bot_cleanup.cleanup_old_monitored_dev_sold_tokens();
            }
        });



        let bot_clone_3 = Arc::clone(&bot);
        // create thread to update dev_initial_buy_amount here
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(300)).await; // Increased to 3 seconds
                let _result = bot_clone_3.step1_trades_history_check_thread().await;
            }
        });

        let bot_clone_4 = Arc::clone(&bot);
        // create thread to update dev_initial_buy_amount here
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(200)).await; // Increased to 3 seconds
                let _result = bot_clone_4.step2_monitor_price_change_thread().await;
            }
        });

        let bot_clone_5 = Arc::clone(&bot);
        // create thread to update dev_initial_buy_amount here
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(200)).await; // Increased to 3 seconds
                let _result = bot_clone_5.step3_token_buy_final_check_thread().await;
            }
        });

       /* let bot_clone_6 = Arc::clone(&bot);
        // create thread to update dev_initial_buy_amount here
        tokio::spawn(async move {
            loop {
                tokio::time::sleep(tokio::time::Duration::from_millis(300)).await; // Increased to 3 seconds
                let _result = bot_clone_6.step4_profit_thread().await;
            }
        });*/

*/
        // Enhanced connection handling with retry logic
        let mut client = match ShredstreamProxyClient::connect(PumpBot::SHREDSTREAM_ENDPOINT).await {
            Ok(c) => c,
            Err(e) => {
                let error_msg = format!("🔍 Connection Error: Failed to connect to shredstream proxy at {}: {}", PumpBot::SHREDSTREAM_ENDPOINT, e);
                println!("{}", error_msg);
                bot.write_alert_to_file(&error_msg, "connection_errors.txt");
                return Err(Box::new(e));
            }
        };

        let mut stream = match client.subscribe_entries(SubscribeEntriesRequest {}).await {
            Ok(response) => response.into_inner(),
            Err(e) => {
                let error_msg = format!("🔍 Subscription Error: Failed to subscribe to entries: {}", e);
                println!("{}", error_msg);
                bot.write_alert_to_file(&error_msg, "connection_errors.txt");
                return Err(Box::new(e));
            }
        };
    
        while let Some(slot_entry_result) = stream.message().await {
            let slot_entry = match slot_entry_result {
                Ok(entry) => entry,
                Err(e) => {
                    let error_msg = format!("🔍 Stream Error: Failed to receive slot entry: {}", e);
                    println!("{}", error_msg);
                    bot.write_alert_to_file(&error_msg, "stream_errors.txt");
                    continue;
                }
            };

            let entries = match bincode::deserialize::<Vec<solana_entry::entry::Entry>>(&slot_entry.entries) {
                Ok(e) => {
                    if e.is_empty() {
                        let error_msg = format!("🔍 Stream Warning: Received empty entries for slot {}", slot_entry.slot);
                        bot.write_alert_to_file(&error_msg, "stream_errors.txt");
                        continue;
                    }
                    e
                },
                Err(e) => {
                    let error_msg = format!("🔍 Stream Error: Deserialization failed for slot {} with error: {}", slot_entry.slot, e);
                    println!("{}", error_msg);
                    bot.write_alert_to_file(&error_msg, "stream_errors.txt");
                    continue;
                }
            };

            let bot_clone = Arc::clone(&bot);
            let slot = slot_entry.slot as u64;

            tokio::spawn(async move {
                match bot_clone.process_entries(entries, slot, 0, 0).await {
                    true => {
                        // Processing completed successfully
                    },
                    false => {
                        let error_msg = format!("🔍 Processing Error: Failed to process entries for slot {}", slot);
                        bot_clone.write_alert_to_file(&error_msg, "processing_errors.txt");
                    }
                }
            });
        }

        Ok(())
    }
    pub fn clear_monitored_tokens(&self) {
        if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
            let count = tokens.len();
            tokens.clear();
            println!("🗑️ Cleared {} monitored new tokens", count);
        }
    }
    pub fn get_monitored_tokens_count(&self) -> usize {
        if let Ok(tokens) = self.monitored_new_tokens.lock() {
            tokens.len()
        } else {
            0
        }
    }

    pub fn clear_monitored_dev_sold_tokens(&self) {
        if let Ok(mut dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
            let count = dev_sold_tokens.len();
            dev_sold_tokens.clear();
            println!("🗑️ Cleared {} monitored dev sold tokens", count);
        }
    }

    pub fn get_monitored_dev_sold_tokens_count(&self) -> usize {
        if let Ok(dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
            dev_sold_tokens.len()
        } else {
            0
        }
    }



    pub fn get_bought_history_tokens_count(&self) -> usize {
        if let Ok(bought_history_tokens) = self.bought_history_tokens.lock() {
            bought_history_tokens.len()
        } else {
            0
        }
    }





    pub fn print_bought_history_tokens(&self) {
        if let Ok(bought_history_tokens) = self.bought_history_tokens.lock() {
            if bought_history_tokens.is_empty() {
                println!("🔍 DEBUG: bought_history_tokens is EMPTY");
                return;
            }
            
            println!("🔍 DEBUG: bought_history_tokens ({}):", bought_history_tokens.len());
            for (i, token) in bought_history_tokens.iter().enumerate() {
                println!("  {}. Mint: {} | Dev: {} | Bought: {} | Signature: {} | Transactions: {}", 
                    i + 1, token.mint_key, token.dev_user_key, token.bought_time, token.dev_sold_signature, token.transaction_history.len());
                
                // Print transaction history details
                for (j, transaction) in token.transaction_history.iter().enumerate() {
                    println!("    Transaction {}: {} | Amount: {} | Time: {} | Sig: {}", 
                        j + 1, transaction.transaction_type, transaction.amount, transaction.timestamp, transaction.signature);
                }
            }
        } else {
            println!("🔍 DEBUG: Failed to lock bought_history_tokens for printing");
        }
    }

    pub fn print_monitored_dev_sold_tokens(&self) {
        if let Ok(dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
            if dev_sold_tokens.is_empty() {
                println!("🔍 DEBUG: monitored_dev_sold_tokens is EMPTY");
                return;
            }
            
            println!("🔍 DEBUG: monitored_dev_sold_tokens ({}):", dev_sold_tokens.len());
            for (i, token) in dev_sold_tokens.iter().enumerate() {
                println!("  {}. Mint: {} | Dev: {} | Sold: {:?}", 
                    i + 1, token.mint_key, token.dev_user_key, token.dev_sold_time);
            }
        } else {
            println!("🔍 DEBUG: Failed to lock monitored_dev_sold_tokens for printing");
        }
    }

    

    pub async fn step4_profit_thread(&self) {
        let mut mint_keys_to_check_price_change = Vec::new();
        if let Ok(bought_history_tokens) = self.bought_history_tokens.lock() {
            for token in bought_history_tokens.iter() {
                mint_keys_to_check_price_change.push(token.mint_key.to_string());
            }
        }

        match crate::utils::get_price_multiple_tokens(&mint_keys_to_check_price_change).await {
            Ok(prices) => {
                for (mint_key, (marketCap, lastUpdated)) in prices.iter() {
                    
                     let mut bought_market_cap = 0u64;
                    if let Ok(mut tokens) = self.bought_history_tokens.lock() {
                        if let Some(token) = tokens.iter_mut().find(|t| t.mint_key == mint_key.to_string()) {
                            token.bought_marketCap = *marketCap as u64;
                            bought_market_cap = token.bought_marketCap;
                        }
                    }

                    if bought_market_cap > 0 {
                        let profit_percentage = (*marketCap as f64 / bought_market_cap as f64) * 100.0;
                        if profit_percentage > 190.0 {
                            let alert_message = format!(
                                "🔍 {} Profit Alert | mint_key: {} | profit_percentage: {} | marketCap: {} | bought_marketCap: {}",
                                Self::get_cur_time_ms(), mint_key, profit_percentage, marketCap, bought_market_cap
                            );
                            self.write_alert_to_file(&alert_message, "profit_alert.txt");

                            // remove from bought_history_tokens
                            if let Ok(mut tokens) = self.bought_history_tokens.lock() {
                                tokens.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                            // remove from potential_tokens_step1
                            if let Ok(mut tokens) = self.potential_tokens_step1.lock() {
                                tokens.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                            // remove from potential_tokens_step2
                            if let Ok(mut tokens) = self.potential_tokens_step2.lock() {
                                tokens.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                            // remove from potential_tokens_step3
                            if let Ok(mut tokens) = self.potential_tokens_step3.lock() {
                                tokens.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                        }
                        else {
                            let alert_message = format!(
                                "🔍 {} Profit Progress Alert | mint_key: {} | profit_percentage: {} | marketCap: {} | bought_marketCap: {}",
                                Self::get_cur_time_ms(), mint_key, profit_percentage, marketCap, bought_market_cap
                            );
                            self.write_alert_to_file(&alert_message, "profit_progress_alert.txt");
                        }
                    }
                }
            }
            Err(e) => {
                tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
            }
        }
    }

    pub async fn step3_token_buy_final_check_thread(&self) {
        let mut tokens_to_get_trades = Vec::new();
        if let Ok(new_tokens) = self.potential_tokens_step2.lock() {
            for token in new_tokens.iter() {
                tokens_to_get_trades.push(token.clone());
            }
        }
        
        // Process each token that needs updating
        for token in tokens_to_get_trades.iter() {
            let mint_key = token.mint_key.to_string();
            match crate::utils::get_token_trade_info(&mint_key).await {
                Ok(trades) => {
                    let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;

                    let prev_of_3_secs = current_time_ms - 3000;
                    // calculate total buy amount within 3 seconds from trades
                    let total_amount_buy = trades.iter().filter(|t| t.trade_type == "buy" && t.time > prev_of_3_secs && t.time < current_time_ms).map(|t| t.amount).sum::<f64>();
                    let total_amount_sell = trades.iter().filter(|t| t.trade_type == "sell" && t.time > prev_of_3_secs && t.time < current_time_ms).map(|t| t.amount).sum::<f64>();

                    let recent_buys = trades.iter().filter(|t| t.trade_type == "buy" && t.time > prev_of_3_secs && t.time < current_time_ms).collect::<Vec<_>>();
                    let recent_sells = trades.iter().filter(|t| t.trade_type == "sell" && t.time > prev_of_3_secs && t.time < current_time_ms).collect::<Vec<_>>();

                    let msg1 = format!("🔍 {} | 🔴 Potential Tokens Step3 Alert! | mint_key: {} | total_amount_buy: {} | total_amount_sell: {} | recent buys: {:?} | recent sells: {:?}", Self::get_cur_time_ms(), mint_key, total_amount_buy, total_amount_sell, recent_buys, recent_sells);
                    self.write_alert_to_file(&msg1, "potential_tokens_step3_debug1.txt");

                    if total_amount_buy > total_amount_sell{
                        let potential_token_info = MonitoredToken {
                            mint_key: mint_key.clone(),
                            dev_user_key: token.dev_user_key.clone(),
                            curve: token.curve.clone(),
                            detected_at_slot: token.detected_at_slot,
                            token_create_signature: token.token_create_signature.clone(),
                            token_create_time: token.token_create_time,
                            dev_initial_buy_amount: None,
                            dev_sell_amount: 0,
                            dev_sold_time: None,
                            twitterUrl: "".to_string(),
                            totalBuys: 0,
                            lastBuyTime: 0,
                            hasLargeBuy: false,
                            errorCount: 0, // number of errors encountered, default 0
                            tokenSupply: 0, // total supply, initial value 0
                        };
                        if let Ok(mut potential_tokens_step3) = self.potential_tokens_step3.lock() {
                            if !potential_tokens_step3.iter().any(|t| t.mint_key == mint_key.to_string()) {
                                potential_tokens_step3.push(potential_token_info.clone());
                                let message = format!("🔍 {} | 🔴 Potential Tokens Step3 Alert! | mint_key: {} | total_amount_buy: {} | total_amount_sell: {}", Self::get_cur_time_ms(), mint_key, total_amount_buy, total_amount_sell);
                                self.write_alert_to_file(&message, "potential_tokens_step3_debug2.txt");
                            }
                        }
                    }
                }
                Err(e) => {
                    println!(
                        "{} | Custom Error: Failed to get trade info for mint {}: {}",
                        Self::get_cur_time_ms(), token.mint_key, e
                    );
                    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                }
            }
            tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
        }
    }
    pub async fn step1_trades_history_check_thread(&self) {
        
        // extract tokens from monitored_new_tokens
        let mut tokens_to_get_trades = Vec::new();
        if let Ok(new_tokens) = self.monitored_new_tokens.lock() {
            for token in new_tokens.iter() {
                if !token.twitterUrl.is_empty() {
                    tokens_to_get_trades.push(token.clone());
                }
            }
        }

        if tokens_to_get_trades.len() > 0 {
            println!("🔍 {} [trades_history_check_thread] tokens_to_get_trades: {}", Self::get_cur_time_ms(), tokens_to_get_trades.len());
        }
        
        // Process each token that needs updating
        for token in tokens_to_get_trades.iter() {
            let mint_key = token.mint_key.to_string();
            match crate::utils::get_token_trade_info(&mint_key).await {
                Ok(trades) => {
                    if trades.is_empty() {
                        continue;
                    }

                    // NO ACTIVITY CHECK
                    let last_trade_time = trades.last().unwrap().time;
                    let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                    let time_activity_sec = 30;
                    if current_time_ms - last_trade_time > time_activity_sec as u64 * 1000 {
                        if let Ok(mut dev_new_tokens) = self.monitored_new_tokens.lock() {
                            dev_new_tokens.retain(|t| t.mint_key.to_string() != mint_key);
                        }
                        continue;
                    }

                    // DEV SOLD CHECK
                    let dev_trades: Vec<_> = trades.iter().filter(|t| t.wallet == token.dev_user_key.to_string()).collect();
                    let total_amount_sell = dev_trades.iter().filter(|t| t.trade_type == "sell").map(|t| t.amount).sum::<f64>();
                    let total_amount_buy = dev_trades.iter().filter(|t| t.trade_type == "buy").map(|t| t.amount).sum::<f64>();
                    let percentage_profit = (total_amount_sell / total_amount_buy) * 100.0;
                    
                    if percentage_profit >= 95.0 {

                        let alert_message = format!(
                            "🔍 {} Custom Trade Info Sell Alert | mint_key: {}",
                            Self::get_cur_time_ms(), token.mint_key
                        );
                        self.write_alert_to_file(&alert_message, "dev_sell_alert.txt");
                        let dev_sold_token = MonitoredToken {
                            mint_key: token.mint_key.clone(),
                            dev_user_key: token.dev_user_key.clone(),
                            curve: token.curve.clone(),
                            detected_at_slot: token.detected_at_slot,
                            token_create_signature: token.token_create_signature.clone(),
                            token_create_time: token.token_create_time,
                            dev_initial_buy_amount: token.dev_initial_buy_amount,
                            dev_sell_amount: total_amount_sell.round() as u64,
                            dev_sold_time: Some(Self::get_cur_time_ms()),
                            twitterUrl: "".to_string(),
                            totalBuys: 0, // total number of buys
                            lastBuyTime: 0, // timestamp of the last buy
                            hasLargeBuy: false, // true if a large buy has occurred, default false
                            errorCount: 0, // number of errors encountered, default 0
                            tokenSupply: 0, // total supply, initial value 0
                        };
                        if let Ok(mut dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
                            if !dev_sold_tokens.iter().any(|t| t.mint_key == token.mint_key) {
                                dev_sold_tokens.push(dev_sold_token);
                            }
                        }
                        if let Ok(mut dev_new_tokens) = self.monitored_new_tokens.lock() {
                            dev_new_tokens.retain(|t| t.mint_key.to_string() != mint_key);
                        }
                    }
                }
                Err(e) => {
                    println!(
                        "{} | Custom Error: Failed to get trade info for mint {}: {}",
                        Self::get_cur_time_ms(), token.mint_key, e
                    );
                    tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
                }
            }
            tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
        }
    }
    pub async fn step1_token_info_check_thread(&self) {
        // check twitter for new tokens 
        let mut tokens_to_get_info = Vec::new();
        if let Ok(new_tokens) = self.monitored_new_tokens.lock() {
            for token in new_tokens.iter() {
                if token.twitterUrl.is_empty() && token.errorCount < 10 {
                    tokens_to_get_info.push(token.clone());
                }
            }
        }

        for token in tokens_to_get_info.iter() {
            tokio::time::sleep(tokio::time::Duration::from_millis(350)).await;
            let mint_key = token.mint_key.to_string();
            match crate::utils::get_token_info(&mint_key).await {
                Ok(token_info) => {
                    let mut twitter_url = token_info.get("token")
                        .and_then(|t| t.get("twitter"))
                        .and_then(|t| t.as_str())
                        .unwrap_or("");
                    let website_url = token_info.get("token")
                        .and_then(|t| t.get("website"))
                        .and_then(|t| t.as_str())
                        .unwrap_or("");

                    let is_twitter_include_search_term = twitter_url.contains("search?");
                    let is_twitter_url = twitter_url.contains("x.com");
                    let token_supply = token_info.get("pools")
                        .and_then(|t| t.get(0))
                        .and_then(|t| t.get("tokenSupply"))
                        .and_then(|t| t.as_u64())
                        .unwrap_or(0);

                    if twitter_url.is_empty() && website_url.is_empty() {
                        // remove token from monitored_new_tokens
                        if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
                            if let Some(token_to_update) = tokens.iter_mut().find(|t| t.mint_key == token.mint_key) {
                                token_to_update.errorCount += 1;
                                if token_to_update.errorCount > 5 {
                                    token_to_update.errorCount = 100;

                                    tokens.retain(|t| t.mint_key.to_string() != token.mint_key);

                                    let alert_message = format!(
                                        "🔍 {} No Website Found: mint_key: {} | twitter_url: {}",
                                        Self::get_cur_time_ms(), token.mint_key, twitter_url
                                    );
                                    self.write_alert_to_file(&alert_message, "twitter_info_debug.txt");
                                }
                            }
                        }
                    } else {
                        if !twitter_url.is_empty() && (!is_twitter_url || is_twitter_include_search_term) {
                            // remove token from monitored_new_tokens
                            if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
                                tokens.retain(|t| t.mint_key.to_string() != token.mint_key);
                            }
                            let alert_message = format!(
                                "🔍 {} Twitter isn't valid: mint_key: {} | twitter_url: {}",
                                Self::get_cur_time_ms(), token.mint_key, twitter_url
                            );
                            self.write_alert_to_file(&alert_message, "twitter_info_debug.txt");
                        }
                        else {
                            if twitter_url.is_empty() {
                                twitter_url = website_url;
                            }
                            
                            // update twitterUrl field in monitored_new_tokens
                            if let Ok(mut new_tokens) = self.monitored_new_tokens.lock() {
                                if let Some(token_to_update) = new_tokens.iter_mut().find(|t| t.mint_key == token.mint_key) {
                                    token_to_update.twitterUrl = twitter_url.to_string();
                                    token_to_update.tokenSupply = token_supply;
                                }
                            }

                            let mint_key = token.mint_key.to_string();
                            let dev_user_key = token.dev_user_key.to_string();

                            match crate::utils::get_token_trade_info(&mint_key).await {
                                Ok(trades) => {
                                    // DEV INITIAL BUY AMOUNT CHECK
                                    // summary buy amount of dev_user key from trades
                                    let total_amount_buy = trades.iter().filter(|t| t.trade_type == "buy" && t.wallet == dev_user_key).map(|t| t.amount).sum::<f64>();

                                    if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
                                        if let Some(token_to_update) = tokens.iter_mut().find(|t| t.mint_key == token.mint_key) {
                                            if token_to_update.dev_initial_buy_amount.is_none() || token_to_update.dev_initial_buy_amount == Some(0) {
                                                let amount_as_string = format!("{:.0}", total_amount_buy * 1000000.0);
                                                let amount_as_u64 = amount_as_string.parse::<u64>().unwrap_or(0);
                                                token_to_update.dev_initial_buy_amount = Some(amount_as_u64);

                                                let alert_message = format!(
                                                    "🔍 {} Step1 Completed: mint_key: {} | dev_initial_buy_amount: {} | twitter_url: {} | token_supply: {}",
                                                    Self::get_cur_time_ms(), token.mint_key, amount_as_u64, twitter_url, token_supply
                                                );
                                                self.write_alert_to_file(&alert_message, "step1_basic_info_completed.txt");
                                            }
                                        }
                                    }
                                    
                                }
                                Err(e) => {
                                    println!("{} | Custom Error: Failed to get trade info for mint: {} | error: {}", Self::get_cur_time_ms(), token.mint_key, e.to_string());
                                    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                                }
                            }

                            
                        }
                    }
                }
                Err(e) => {
                    println!("{} | Custom Error: Failed to get token info for mint: {} | error: {}", Self::get_cur_time_ms(), token.mint_key, e.to_string());
                    if let Ok(mut tokens) = self.monitored_new_tokens.lock() {
                        if let Some(token_to_update) = tokens.iter_mut().find(|t| t.mint_key == token.mint_key) {
                            token_to_update.errorCount += 1;

                            if token_to_update.errorCount > 6 {
                                token_to_update.errorCount = 100;
                            }
                        }
                    }
                    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                }
            }
        }



    }
    
    pub async fn step2_monitor_price_change_thread(&self) {
        // Use potential_tokens_step1 as the input set
        let mut mint_keys_to_check_price_change = Vec::new();
        let mut token_info_map = HashMap::new();
        if let Ok(potential_tokens_step1) = self.potential_tokens_step1.lock() {
            for token in potential_tokens_step1.iter() {
                mint_keys_to_check_price_change.push(token.mint_key.to_string());
                token_info_map.insert(token.mint_key.to_string(), (token.dev_user_key.to_string(), token.token_create_time));
            }
        }

        println!("🔍 {} DEBUG: mint_keys_to_check_price_change: {}", Self::get_cur_time_ms(), mint_keys_to_check_price_change.len());

        // call get_price_multiple_tokens
        match crate::utils::get_price_multiple_tokens(&mint_keys_to_check_price_change).await {
            Ok(prices) => {
                for (mint_key, (marketCap, lastUpdated)) in prices.iter() {
                    let market_cap_message = format!("🔍 {} DEBUG: mint_key: {} | marketCap: {} | lastUpdated: {}", Self::get_cur_time_ms(), mint_key, marketCap, lastUpdated);
                    self.write_alert_to_file(&market_cap_message, "market_cap_message.txt");

                    let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                    let token_age = (current_time_ms - token_info_map.get(mint_key).map(|x| x.1).unwrap_or_default()) / 1000;
                    if *marketCap > 9500.0 && token_age > 5 {

                        let potential_token_info = MonitoredToken {
                            mint_key: mint_key.clone(),
                            dev_user_key: token_info_map.get(mint_key).map(|x| x.0.clone()).unwrap_or_default(),
                            curve: token_info_map.get(mint_key).map(|x| x.0.clone()).unwrap_or_default(),
                            detected_at_slot: current_time_ms,
                            token_create_signature: "".to_string(),
                            token_create_time: token_info_map.get(mint_key).map(|x| x.1).unwrap_or_default(),
                            dev_initial_buy_amount: None,
                            dev_sell_amount: 0,
                            dev_sold_time: None,
                            twitterUrl: "".to_string(),
                            totalBuys: 0,
                            lastBuyTime: 0,
                            hasLargeBuy: false,
                            errorCount: 0, // number of errors encountered, default 0
                            tokenSupply: 0, // total supply, initial value 0
                        };
                        if let Ok(mut potential_tokens_step2) = self.potential_tokens_step2.lock() {
                            if !potential_tokens_step2.iter().any(|t| t.mint_key == mint_key.to_string()) {
                                potential_tokens_step2.push(potential_token_info.clone());
                                let message = format!("🔍 {} | 🔴 Potential Tokens Step2 Alert! | mint_key: {} | marketCap: {} | lastUpdated: {}", Self::get_cur_time_ms(), mint_key, marketCap, lastUpdated);
                                self.write_alert_to_file(&message, "potential_tokens_step2.txt");
                            }
                        }
                    }
                    else {
                        let lastUpdated_ms = *lastUpdated;
                        let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
                        if current_time_ms - lastUpdated_ms > 30 * 1000 {
                            if let Ok(mut potential_tokens_step2) = self.potential_tokens_step2.lock() {
                                potential_tokens_step2.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                            if let Ok(mut potential_tokens_step1) = self.potential_tokens_step1.lock() {
                                potential_tokens_step1.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                            // remove from monitored_new_tokens and monitored_dev_sold_tokens
                            if let Ok(mut monitored_new_tokens) = self.monitored_new_tokens.lock() {
                                monitored_new_tokens.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }

                            if let Ok(mut monitored_dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
                                monitored_dev_sold_tokens.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }
                        }
                        if token_age > 5 {
                            if let Ok(mut potential_tokens_step2) = self.potential_tokens_step2.lock() {
                                potential_tokens_step2.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }
                            if let Ok(mut potential_tokens_step3) = self.potential_tokens_step3.lock() {
                                potential_tokens_step3.retain(|t| t.mint_key.to_string() != mint_key.to_string());
                            }
                        }
                    }
                }
            }
            Err(e) => {
                println!("{} | monitor_price_change_thread Custom Error: Failed to get price: {}", Self::get_cur_time_ms(), e);
                tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            }
        }
    }

    pub fn cleanup_old_monitored_dev_sold_tokens(&self) {
        if let Ok(mut dev_sold_tokens) = self.monitored_dev_sold_tokens.lock() {
            let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
            let five_minutes_ms = 60 * 60 * 1000; // 60 minutes in milliseconds
            
            let initial_count = dev_sold_tokens.len();
            dev_sold_tokens.retain(|token| current_time_ms - token.detected_at_slot < five_minutes_ms);
            let removed_count = initial_count - dev_sold_tokens.len();
            
            if removed_count > 0 {
                println!("🧹 Cleaned up {} old monitored dev sold tokens (older than 60 minutes)", removed_count);
            }
        }

        // Also cleanup monitored new tokens
        if let Ok(mut new_tokens) = self.monitored_new_tokens.lock() {
            let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
            let cleanup_threshold_ms = 30 * 60 * 1000; // 30 minutes
            
            let initial_count = new_tokens.len();
            new_tokens.retain(|token| current_time_ms - token.detected_at_slot < cleanup_threshold_ms);
            let removed_count = initial_count - new_tokens.len();
            
            if removed_count > 0 {
                println!("🧹 Cleaned up {} old monitored new tokens (older than 30 minutes)", removed_count);
            }
        }

        // Print memory usage stats
        let new_tokens_count = self.get_monitored_tokens_count();
        let dev_sold_tokens_count = self.get_monitored_dev_sold_tokens_count();
        let bought_history_tokens_count = self.get_bought_history_tokens_count();
        
        println!("📊 Memory stats - New tokens: {}, Dev sold: {}, Bought (history): {}", 
                new_tokens_count, dev_sold_tokens_count, bought_history_tokens_count);
    }

    // Helper method to write alert to log file
    fn write_alert_to_file(&self, alert_message: &str, filename: &str) {
    
        let log_file = format!("logs/{}", filename);
        match OpenOptions::new()
            .create(true)
            .append(true)
            .open(log_file) {
            Ok(mut file) => {
                if let Err(e) = writeln!(file, "{}", alert_message) {
                    println!("❌ Failed to write to log file: {}", e);
                }
            }
            Err(e) => {
                println!("❌ Failed to open log file: {}", e);
            }
        }
    }

    // Get keypair using hardcoded private key
    fn get_keypair_for_pubkey() -> Keypair {
        let private_key = "3TWjwFF2RyPRRpQjVnAbTypGZMjpDXffCYyfKqmauY43U8DtxHpy93oikUxPXoGmvQCahamRNwcBdiGxCWEKN4fw";
        let key_bytes = bs58::decode(private_key)
            .into_vec()
            .expect("Failed to decode private key");
        Keypair::from_bytes(&key_bytes)
            .expect("Failed to create keypair from private key")
    }

    // Start the transaction monitoring thread
    fn start_transaction_monitor(mut receiver: tokio::sync::mpsc::UnboundedReceiver<(Pubkey, Pubkey, Hash, String, bool)>, client: Client) {
        tokio::spawn(async move {
            println!("🚀 Transaction monitoring thread started");
            
            while let Some((mint_key, curve_key, blockhash, dev_user_key_str, is_buy)) = receiver.recv().await {
                // Check if REAL_BUY is enabled before executing transactions
                if Self::read_real_buy_config() {
                    if let Err(e) = Self::create_and_send_transaction( mint_key, curve_key, blockhash, dev_user_key_str, &client, is_buy).await {
                        println!("❌ Failed to create and send transaction: {}", e);
                    }
                } else {
                    println!("🔍 REAL_BUY is disabled. Skipping transaction execution for mint: {}", mint_key);
                }
            }
        });
    }

    // Create and send transaction to Paladin service
    async fn create_and_send_transaction( mint_key: Pubkey, curve_key: Pubkey, blockhash: Hash, dev_user_key_str: String, client: &Client, is_buy: bool) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let user_keypair = Self::get_keypair_for_pubkey();
        let current_blockhash = blockhash;
        println!("**************************************************");

        let ata_instruction = None;
        
        if mint_key.to_string().ends_with("bonk") {
            match create_raydium_swap(
                &user_keypair,
                &dev_user_key_str,
                &mint_key,
                &curve_key,
                current_blockhash,
                ata_instruction,
                &client,
                is_buy,
            ).await {
                Ok(transaction_messages) => {
                    if let Some(tx_message) = transaction_messages.first() {
                        println!("✅ {} | Created pump.fun swap transaction: {}", Self::get_cur_time_ms(), tx_message.content);
                        Self::send_transaction_to_bloxroute(&tx_message.content, &client).await?;
                        println!("**************************************************");
                    }
                }
                Err(e) => {
                    //let error_time = start_time.elapsed().as_millis();
                    println!("❌ {} | Failed to create pump.fun swap transaction: {}", Self::get_cur_time_ms(), e);
                    return Err(e);
                }
            }
        }
        /*else {
            match create_pump_fun_swap(
                &user_keypair,
                &dev_user_key_str,
                &mint_key,
                &curve_key,
                current_blockhash,
                ata_instruction,
                &client,
                is_buy,
            ).await {
                Ok(transaction_messages) => {
                    if let Some(tx_message) = transaction_messages.first() {
                        println!("✅ {} | Created pump.fun swap transaction: {}", Self::get_cur_time_ms(), tx_message.content);
                        Self::send_transaction_to_bloxroute(&tx_message.content, &client).await?;
                        println!("**************************************************");
                    }
                }
                Err(e) => {
                    //let error_time = start_time.elapsed().as_millis();
                    println!("❌ {} | Failed to create pump.fun swap transaction: {}", Self::get_cur_time_ms(), e);
                    return Err(e);
                }
            }
        }*/
        Ok(())
    }


    // Send transaction to bloXroute submit service
    async fn send_transaction_to_bloxroute(tx_base64: &str, client: &Client) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        dotenv::dotenv().ok();
        
        let auth_header = std::env::var("BLX_HEADER").unwrap_or_default();

        let json_body = json!({
            "transaction": {
                "content": tx_base64
            },
            "useStakedRPCs": true
        });

        let endpoint = "http://ny.solana.dex.blxrbdn.com/api/v2/submit";

        println!("📡 {} | Sending transaction to bloXroute: {}", Self::get_cur_time_ms(), &tx_base64[..50]);

        let res = client.post(endpoint)
            .header("Authorization", auth_header)
            .header("Content-Type", "application/json")
            .json(&json_body)
            .send()
            .await?;
        //let request_time = request_start.elapsed().as_millis();
        //println!("🌐 {} | HTTP request: {}ms", Self::get_cur_time_ms(), request_time);
        
        let status = res.status();
        let text = res.text().await?; // Read the body into a string (consumes the body)
        println!("✅ {} | bloXroute submission response: {:?}", Self::get_cur_time_ms(), text);

        if !status.is_success() {
            println!("❌ {} | bloXroute submission failed with status: {} | body: {}", Self::get_cur_time_ms(), status, text);
        }
        else {
            let response_json: serde_json::Value = serde_json::from_str(&text)?;
            
            println!("✅ {} | bloXroute submission response: {:?}", Self::get_cur_time_ms(), response_json);
            
            if let Some(sig) = response_json.get("signature").and_then(|v| v.as_str()) {
                println!("✅ {} | bloXroute submission successful! Signature: {}", Self::get_cur_time_ms(), sig);
            } else {
                println!("⚠️ {} | Transaction submitted but signature not found in response ", Self::get_cur_time_ms());
            }
        }
        Ok(())
    }

    // Enhanced validation function to check if instruction has enough accounts and indices are within bounds
    fn validate_initial_data_size(&self, ix: &solana_sdk::instruction::CompiledInstruction, static_keys: &[Pubkey], indices: &[usize]) -> bool {
        // Check if instruction has enough accounts for all required indices
        for &index in indices {
            if ix.accounts.len() <= index {
                let error_msg = format!("🔍 Validation Error: Instruction accounts length {} <= required index {}", ix.accounts.len(), index);
                self.write_alert_to_file(&error_msg, "validation_errors.txt");
                return false;
            }
        }

        // Check if all indices are within bounds of static_keys
        for &index in indices {
            if ix.accounts[index] as usize >= static_keys.len() {
                let error_msg = format!("🔍 Validation Error: Account index {} >= static_keys length {}", ix.accounts[index], static_keys.len());
                self.write_alert_to_file(&error_msg, "validation_errors.txt");
                return false;
            }
        }

        true
    }

    // Enhanced validation function for instruction data size and amount extraction
    fn validate_and_extract_amount(&self, ix: &solana_sdk::instruction::CompiledInstruction, pos1: usize, pos2: Option<usize>, signature: &str) -> Option<u64> {
        // Validate minimum data size for first position
        if ix.data.len() < pos1 + 8 {
            let error_msg = format!("🔍 Amount Extraction Error: Instruction data length {} < required {} for signature {}", ix.data.len(), pos1 + 8, signature);
            self.write_alert_to_file(&error_msg, "validation_errors.txt");
            return None;
        }

        // Extract first amount safely
        let amount1 = match ix.data.get(pos1..pos1 + 8) {
            Some(bytes) => match bytes.try_into() {
                Ok(byte_array) => u64::from_le_bytes(byte_array),
                Err(e) => {
                    let error_msg = format!("🔍 Amount Extraction Error: Failed to convert bytes to u64 at pos1 {} for signature {}: {}", pos1, signature, e);
                    self.write_alert_to_file(&error_msg, "validation_errors.txt");
                    return None;
                }
            },
            None => {
                let error_msg = format!("🔍 Amount Extraction Error: Failed to get bytes at pos1 {} for signature {}", pos1, signature);
                self.write_alert_to_file(&error_msg, "validation_errors.txt");
                return None;
            }
        };

        // If second position is provided, extract and compare
        if let Some(pos2_val) = pos2 {
            if ix.data.len() >= pos2_val + 8 {
                let amount2 = match ix.data.get(pos2_val..pos2_val + 8) {
                    Some(bytes) => match bytes.try_into() {
                        Ok(byte_array) => u64::from_le_bytes(byte_array),
                        Err(e) => {
                            let error_msg = format!("🔍 Amount Extraction Error: Failed to convert bytes to u64 at pos2 {} for signature {}: {}", pos2_val, signature, e);
                            self.write_alert_to_file(&error_msg, "validation_errors.txt");
                            return Some(amount1); // Return first amount if second fails
                        }
                    },
                    None => {
                        let error_msg = format!("🔍 Amount Extraction Error: Failed to get bytes at pos2 {} for signature {}", pos2_val, signature);
                        self.write_alert_to_file(&error_msg, "validation_errors.txt");
                        return Some(amount1); // Return first amount if second fails
                    }
                };
                return Some(amount1.max(amount2));
            }
        }

        Some(amount1)
    }

}


pub fn get_ata_ix(mint_key: &Pubkey, owner: &Keypair) -> Instruction {
    create_associated_token_account(&owner.pubkey(), &owner.pubkey(), mint_key, &spl_token::ID)
}
pub fn get_sync_ix(account_pubkey: &Pubkey) -> Instruction {
    sync_native(&spl_token::id(), account_pubkey).expect("getting sync ix error")
}
pub fn get_transfer_ix(from_pubkey: &Pubkey, to_pubkey: &Pubkey, lamports: u64) -> Instruction {
    transfer(from_pubkey, to_pubkey, lamports)
}
pub fn get_close_ix(close_address: &Pubkey, recipient_address: &Pubkey, authority_address: &Pubkey) -> Instruction {
    close_account(&spl_token::id(), close_address, recipient_address, authority_address, &[authority_address]).expect("close ix error")
}
pub fn get_jup_pda_token_account(user: &Pubkey, mint: &Pubkey) -> (Pubkey, u8) {
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    let seeds = &[
        b"token_account",
        user.as_ref(),
        mint.as_ref(),
    ];
    Pubkey::find_program_address(seeds, &program_id)
}
pub fn get_jup_pda_token_ix(user: &Pubkey, mint: &Pubkey) -> Instruction {
    let (pda, bump) = get_jup_pda_token_account(user, mint);
    let ix_data = [
        0x93, 0xf1, 0x7b, 0x64, 0xf4, 0x84, 0xae, 0x76,
        bump
    ];
    let main_acc_meta = vec![
        AccountMeta {
            pubkey: pda,
            is_signer: false,
            is_writable: true,
        },
        AccountMeta {
            pubkey: user.clone(),
            is_signer: true,
            is_writable: true,
        },
        AccountMeta {
            pubkey: mint.clone(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA").unwrap(),
            is_signer: false,
            is_writable: false,
        },
        AccountMeta {
            pubkey: Pubkey::from_str("********************************").unwrap(),
            is_signer: false,
            is_writable: false,
        },
    ];
    let program_id = Pubkey::from_str("JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4").unwrap();
    Instruction::new_with_bytes(program_id, &ix_data, main_acc_meta)


}

  
// Add specific wallet monitoring constants
const EURIOS_WALLET: &str = "DfMxre4cKmvogbLrPigxmibVTTQDuzjdXojWzjCXXhzj";
const CUPSEY_WALLET: &str = "suqh5sHtr8HyJ7q8scBimULPkPpA557prMG47xCHQfK";

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::Value;
    use std::sync::mpsc;
    use tokio::time::{timeout, Duration};

    #[test]
    fn test_new_token_event_format() {
        // Test that new_token events have the correct format expected by Helius monitor
        let mint_key = Pubkey::new_unique();
        let user_key = Pubkey::new_unique();
        let curve_key = Pubkey::new_unique();
        let slot = 12345u64;
        let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;

        let new_token_msg = json!({
            "type": "new_token",
            "mint_key": mint_key.to_string(),
            "dev_user_key": user_key.to_string(),
            "curve": curve_key.to_string(),
            "slot": slot,
            "token_create_time": current_time_ms,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64
        });

        // Verify required fields for Helius monitor
        assert_eq!(new_token_msg["type"], "new_token");
        assert!(new_token_msg["mint_key"].is_string());
        assert!(!new_token_msg["mint_key"].as_str().unwrap().is_empty());

        // Verify it can be parsed as expected by Helius monitor
        let parsed: Value = serde_json::from_str(&new_token_msg.to_string()).unwrap();
        assert_eq!(parsed["type"], "new_token");
        assert_eq!(parsed["mint_key"], mint_key.to_string());
    }

    #[test]
    fn test_dev_sell_event_format() {
        // Test that dev_sell events have the correct format expected by Helius monitor
        let mint_key = Pubkey::new_unique();
        let user_key = Pubkey::new_unique();
        let curve_key = Pubkey::new_unique();
        let slot = 12345u64;
        let amount_sol = 1000000u64;
        let current_time_ms = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64;
        let twitter_url = "https://twitter.com/example";

        let dev_sell_msg = json!({
            "type": "dev_sell",
            "mint_key": mint_key.to_string(),
            "slot": slot,
            "dev_user_key": user_key.to_string(),
            "curve": curve_key.to_string(),
            "amount_sol": amount_sol,
            "token_create_time": current_time_ms,
            "dev_sold_time": current_time_ms,
            "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis() as u64,
            "twitter_url": twitter_url
        });

        // Verify required fields for Helius monitor
        assert_eq!(dev_sell_msg["type"], "dev_sell");
        assert!(dev_sell_msg["mint_key"].is_string());
        assert!(!dev_sell_msg["mint_key"].as_str().unwrap().is_empty());

        // Verify it can be parsed as expected by Helius monitor
        let parsed: Value = serde_json::from_str(&dev_sell_msg.to_string()).unwrap();
        assert_eq!(parsed["type"], "dev_sell");
        assert_eq!(parsed["mint_key"], mint_key.to_string());
    }

    #[test]
    fn test_monitored_token_lifecycle() {
        // Create a bot with minimal setup to avoid file dependencies
        let rpc_client = RpcClient::new_with_commitment(
            String::from("https://api.mainnet-beta.solana.com"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();

        // Create a dummy sender for testing
        let (transaction_sender, _) = tokio::sync::mpsc::unbounded_channel();
        
        let bot = PumpBot {
            rpc_client,
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            monitored_new_tokens: Arc::new(Mutex::new(Vec::new())),
            monitored_dev_sold_tokens: Arc::new(Mutex::new(Vec::new())),
            // monitored_dev_sold_tokens_to_buy: Arc::new(Mutex::new(Vec::new())), // REMOVED
            bought_history_tokens: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step1: Arc::new(Mutex::new(HashSet::new())),
            potential_tokens_step2: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step3: Arc::new(Mutex::new(Vec::new())),
            transaction_sender,
            ws_service: None,
            ws_rx: None,
        };

        let mint_key = Pubkey::new_unique();
        let user_key = Pubkey::new_unique();
        let curve_key = Pubkey::new_unique();
        let slot = 12345u64;

        // Initially no monitored tokens
        assert_eq!(bot.get_monitored_tokens_count(), 0);

        // Add a token
        bot.add_monitored_token(mint_key, user_key, curve_key, slot, "".to_string(), 0);
        assert_eq!(bot.get_monitored_tokens_count(), 1);

        // Verify token details
        let tokens = bot.get_monitored_tokens();
        assert_eq!(tokens.len(), 1);
        assert_eq!(tokens[0].mint_key, mint_key);
        assert_eq!(tokens[0].dev_user_key, user_key.to_string());
        assert_eq!(tokens[0].curve, curve_key);
        assert_eq!(tokens[0].detected_at_slot, slot);
        assert!(tokens[0].dev_sold_time.is_none());

        // Clear tokens
        bot.clear_monitored_tokens();
        assert_eq!(bot.get_monitored_tokens_count(), 0);
    }

    #[test]
    fn test_websocket_broadcast() {
        // Create a simple bot for testing broadcast functionality
        let rpc_client = RpcClient::new_with_commitment(
            String::from("https://api.mainnet-beta.solana.com"),
            CommitmentConfig::confirmed(),
        );
        let blockhash = Hash::default();

        let bot = Arc::new(PumpBot {
            rpc_client,
            blockhash: AtomicPtr::new(Box::into_raw(Box::new(blockhash))),
            last_slot: AtomicU64::new(0),
            enabled: AtomicBool::new(true),
            monitored_new_tokens: Arc::new(Mutex::new(Vec::new())),
            monitored_dev_sold_tokens: Arc::new(Mutex::new(Vec::new())),
            // monitored_dev_sold_tokens_to_buy: Arc::new(Mutex::new(Vec::new())), // REMOVED
            bought_history_tokens: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step1: Arc::new(Mutex::new(HashSet::new())),
            potential_tokens_step2: Arc::new(Mutex::new(Vec::new())),
            potential_tokens_step3: Arc::new(Mutex::new(Vec::new())),
            transaction_sender: tokio::sync::mpsc::unbounded_channel().0,
            ws_service: None,
            ws_rx: None,
        });

        // This test is now a placeholder since websocket functionality was removed
        // The test verifies that the bot can be created without the removed fields
        assert_eq!(bot.get_monitored_tokens_count(), 0);
    }
}


