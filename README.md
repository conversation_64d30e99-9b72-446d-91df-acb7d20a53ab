# Jito Shredstream Proxy

ShredStream provides the lowest latency to shreds from leaders on Solana. 

See more at https://docs.jito.wtf/lowlatencytxnfeed/

## pre install
sudo apt update
sudo apt install libssl-dev pkg-config

sudo apt-get update
sudo apt-get install libudev-dev pkg-config protobuf-compiler
sudo apt install -y libssl3 libssl-dev
sudo apt update && sudo apt install -y \
  build-essential \
  cmake \
  clang \
  llvm \
  libclang-dev \
  libssl-dev \
  pkg-config \
  libudev-dev \
  libsqlite3-dev \
  libzstd-dev \
  protobuf-compiler \
  libprotobuf-dev \
  libsodium-dev \
  libpq-dev \
  libxml2-dev \
  libxslt1-dev \
  libcurl4-openssl-dev \
  libreadline-dev \
  libbz2-dev \
  libsnappy-dev \
  liblz4-dev \
  zlib1g-dev


## Disclaimer
Use this at your own risk.

## How to build and run project

cargo build -p pump-tester --release


./target/release/pump-tester --release
